# 阿里云人机验证SDK接入计划（Web端短信验证）

## 项目背景

当前uniapp vue2项目是一个多端金融H5应用，支持微信小程序、H5、微信公众号等多端部署。项目中已有完善的短信验证码发送机制，现需要在web端发送短信验证码前增加阿里云人机验证功能。

## 需求分析

- **目标**：在web端使用阿里云人机验证替换现有的短信验证码
- **范围**：仅处理H5/web端，其他平台不考虑
- **场景**：用户在需要验证的场景下，使用人机验证代替短信验证码进行身份验证

## 技术方案

### 1. SDK引入和基础配置

#### 1.1 修改index.html
在项目根目录的`index.html`文件中引入阿里云验证码SDK：

```html
<!-- 在<head>标签中添加 -->
<script>
  window.AliyunCaptchaConfig = {
    region: 'cn',
    prefix: 'your-prefix', // 需要配置实际的前缀
  };
</script>
<script type="text/javascript" src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"></script>
```

### 2. 组件开发

#### 2.1 直接使用demo中的验证码组件
基于vue2-captcha-demo中的组件（CaptchaA和CaptchaB完全相同），创建适用于当前项目的验证码组件。

位置：`components/captcha/aliyun-captcha.vue`

功能：
- 直接复用demo中的验证码逻辑
- 适配uni-app环境
- 在验证成功后直接完成身份验证
- 完全替代短信验证码功能

#### 2.2 替换现有短信验证码流程
将原有的短信验证码流程替换为人机验证：

```javascript
// 原来的流程：输入手机号 → 获取短信验证码 → 输入验证码 → 验证通过
// 新的流程：输入手机号 → 点击按钮 → 人机验证 → 验证通过

async verifyIdentity() {
  // 直接进行人机验证
  this.showCaptcha = true // 显示验证码
}

// 验证码验证成功后的回调
onCaptchaSuccess(captchaVerifyParam) {
  // 验证通过，直接进行下一步操作（如登录、注册等）
  this.submitForm(captchaVerifyParam)
}
```

### 3. 业务页面集成

#### 3.1 页面改造方式

**替换短信验证码流程**，将原有的短信验证码输入框和获取验证码按钮替换为人机验证：

```vue
<template>
  <!-- 原来的短信验证码UI -->
  <!--
  <input v-model="form.code" placeholder="请输入验证码" />
  <button @click="clickGetCode" :disabled="codeCountdown > 0">
    {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
  </button>
  -->

  <!-- 替换为人机验证按钮 -->
  <button @click="startVerification">
    身份验证
  </button>

  <!-- 人机验证组件 -->
  <aliyun-captcha
    v-if="showCaptcha"
    @success="onCaptchaSuccess"
    @fail="onCaptchaFail"
  />
</template>

<script>
export default {
  data() {
    return {
      showCaptcha: false,
      isVerified: false, // 标记是否已通过验证
      // 移除原有的验证码相关数据
      // form: { code: '' }, // 不再需要
      // codeCountdown: 0,   // 不再需要
    }
  },
  methods: {
    // 开始身份验证
    startVerification() {
      this.showCaptcha = true
    },

    // 验证码验证成功回调
    onCaptchaSuccess(captchaVerifyParam) {
      this.showCaptcha = false
      this.isVerified = true

      // 验证通过后，直接进行业务操作（如登录、注册等）
      this.submitForm(captchaVerifyParam)
    },

    // 验证码验证失败回调
    onCaptchaFail(error) {
      this.showCaptcha = false
      uni.showToast({
        title: '验证失败，请重试',
        icon: 'none'
      })
    },

    // 提交表单（原来需要验证码的地方）
    async submitForm(captchaVerifyParam) {
      // 直接提交，不再需要短信验证码
      const res = await saveLoan({
        phone: this.form.phone,
        channelId: this.form.channelId,
        captchaVerifyParam // 使用人机验证参数
        // code: this.form.code // 不再需要短信验证码
      })
      // 处理结果...
    }
  }
}
</script>
```

### 4. 实施步骤

#### 第一阶段：基础设施搭建（1天）
1. ✅ 修改index.html引入SDK
2. ✅ 复制demo中的验证码组件到项目中
3. ✅ 适配uni-app环境

#### 第二阶段：核心功能开发（1-2天）
1. ✅ 在1-2个版本的页面中集成验证码
2. ✅ 修改getCodeHandler方法增加验证步骤
3. ✅ 测试验证码+短信发送的完整流程

#### 第三阶段：全面部署（2-3天）
1. ✅ 在所有需要的页面中集成验证码组件
2. ✅ 功能测试和兼容性验证
3. ✅ 用户体验优化

### 5. 技术实现要点

#### 5.1 用户交互流程
1. 用户输入手机号
2. 点击"获取验证码"按钮
3. 弹出阿里云人机验证界面
4. 用户完成滑块或点选验证
5. 验证通过后自动发送短信验证码（使用原有接口）
6. 开始60秒倒计时，按钮变为不可点击状态

#### 5.2 组件复用
- 直接复用vue2-captcha-demo中的验证码组件代码
- CaptchaA和CaptchaB完全相同，选择其中一个即可
- 只需要适配uni-app的语法差异（div改为view等）

#### 5.3 最小改动原则
- 不改造现有的短信发送接口
- 不替换现有的UI组件
- 只在getCodeHandler方法中增加验证步骤
- 保持原有的倒计时和错误处理逻辑

### 6. 文件清单

#### 新建文件
- `components/captcha/aliyun-captcha.vue` - 验证码组件（复制自demo）

#### 修改文件
- `index.html` - 引入SDK
- 具体需要改造的页面 - 根据后续需求确定

### 7. 预期效果

实施完成后，用户在web端获取短信验证码的体验：
1. 点击"获取验证码"按钮
2. 弹出人机验证界面（滑块或点选）
3. 验证通过后自动发送短信（使用原有sendSmsCode接口）
4. 显示"发送成功"提示
5. 按钮进入60秒倒计时状态

**核心优势**：
- 对现有代码改动最小
- 不需要改造任何API接口
- 直接复用成熟的demo代码
- 保持原有用户体验
